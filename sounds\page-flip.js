// ملف إنشاء أصوات تقليب الصفحات باستخدام Web Audio API

class PageFlipSounds {
  constructor() {
    this.audioContext = null;
    this.sounds = [];
    this.init();
  }

  async init() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.createSounds();
    } catch (error) {
      console.log('Web Audio API not supported');
    }
  }

  createSounds() {
    // إنشاء أصوات مختلفة لتقليب الصفحات
    const soundConfigs = [
      { freq: [800, 200], duration: 0.15, type: 'sine' },
      { freq: [600, 150], duration: 0.12, type: 'triangle' },
      { freq: [900, 250], duration: 0.18, type: 'sine' },
    ];

    soundConfigs.forEach(config => {
      this.sounds.push(this.createSound(config));
    });
  }

  createSound(config) {
    return () => {
      if (!this.audioContext) return;

      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();
      const filter = this.audioContext.createBiquadFilter();

      // ربط العقد
      oscillator.connect(filter);
      filter.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      // إعداد المذبذب
      oscillator.type = config.type;
      oscillator.frequency.setValueAtTime(config.freq[0], this.audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(config.freq[1], this.audioContext.currentTime + config.duration);

      // إعداد المرشح
      filter.type = 'lowpass';
      filter.frequency.setValueAtTime(2000, this.audioContext.currentTime);
      filter.frequency.exponentialRampToValueAtTime(500, this.audioContext.currentTime + config.duration);

      // إعداد مستوى الصوت
      gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + config.duration);

      // تشغيل الصوت
      oscillator.start(this.audioContext.currentTime);
      oscillator.stop(this.audioContext.currentTime + config.duration);
    };
  }

  playRandomSound() {
    if (this.sounds.length === 0) return;
    
    const randomSound = this.sounds[Math.floor(Math.random() * this.sounds.length)];
    randomSound();
  }

  // إنشاء صوت ورق حقيقي
  createPaperSound() {
    if (!this.audioContext) return;

    // إنشاء ضوضاء بيضاء للورق
    const bufferSize = this.audioContext.sampleRate * 0.1;
    const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
    const output = buffer.getChannelData(0);

    // إنشاء ضوضاء بيضاء
    for (let i = 0; i < bufferSize; i++) {
      output[i] = Math.random() * 2 - 1;
    }

    const whiteNoise = this.audioContext.createBufferSource();
    whiteNoise.buffer = buffer;

    const filter = this.audioContext.createBiquadFilter();
    filter.type = 'bandpass';
    filter.frequency.value = 1000;
    filter.Q.value = 5;

    const gainNode = this.audioContext.createGain();
    gainNode.gain.setValueAtTime(0.05, this.audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.1);

    whiteNoise.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    whiteNoise.start(this.audioContext.currentTime);
    whiteNoise.stop(this.audioContext.currentTime + 0.1);
  }
}

// تصدير الكلاس
window.PageFlipSounds = PageFlipSounds;
