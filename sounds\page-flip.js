// ملف إنشاء صوت تقليب الصفحات الحقيقي باستخدام Web Audio API

class PageFlipSounds {
  constructor() {
    this.audioContext = null;
    this.init();
  }

  async init() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (error) {
      console.log('Web Audio API not supported');
    }
  }

  // إنشاء صوت تقليب صفحة حقيقي
  createRealisticPageFlip() {
    if (!this.audioContext) return;

    const now = this.audioContext.currentTime;

    // المرحلة الأولى: صوت رفع الصفحة (ضوضاء ناعمة)
    this.createPaperRustle(now, 0.08, 1200, 0.03);

    // المرحلة الثانية: صوت تقليب الصفحة (نقرة خفيفة)
    this.createPageSnap(now + 0.06, 0.04);

    // المرحلة الثالثة: صوت وضع الصفحة (ضوضاء أخف)
    this.createPaperSettle(now + 0.1, 0.06, 800, 0.02);
  }

  // صوت حفيف الورق
  createPaperRustle(startTime, duration, frequency, volume) {
    if (!this.audioContext) return;

    // إنشاء ضوضاء وردية (أكثر واقعية من البيضاء)
    const bufferSize = this.audioContext.sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
    const output = buffer.getChannelData(0);

    let b0 = 0, b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0;

    for (let i = 0; i < bufferSize; i++) {
      const white = Math.random() * 2 - 1;
      b0 = 0.99886 * b0 + white * 0.0555179;
      b1 = 0.99332 * b1 + white * 0.0750759;
      b2 = 0.96900 * b2 + white * 0.1538520;
      b3 = 0.86650 * b3 + white * 0.3104856;
      b4 = 0.55000 * b4 + white * 0.5329522;
      b5 = -0.7616 * b5 - white * 0.0168980;
      output[i] = b0 + b1 + b2 + b3 + b4 + b5 + b6 + white * 0.5362;
      output[i] *= 0.11;
      b6 = white * 0.115926;
    }

    const source = this.audioContext.createBufferSource();
    source.buffer = buffer;

    const filter = this.audioContext.createBiquadFilter();
    filter.type = 'bandpass';
    filter.frequency.value = frequency;
    filter.Q.value = 2;

    const gainNode = this.audioContext.createGain();
    gainNode.gain.setValueAtTime(volume, startTime);
    gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

    source.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    source.start(startTime);
    source.stop(startTime + duration);
  }

  // صوت نقرة تقليب الصفحة
  createPageSnap(startTime, duration) {
    if (!this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();
    const filter = this.audioContext.createBiquadFilter();

    oscillator.type = 'triangle';
    oscillator.frequency.setValueAtTime(400, startTime);
    oscillator.frequency.exponentialRampToValueAtTime(100, startTime + duration);

    filter.type = 'highpass';
    filter.frequency.value = 200;

    gainNode.gain.setValueAtTime(0.08, startTime);
    gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.start(startTime);
    oscillator.stop(startTime + duration);
  }

  // صوت استقرار الصفحة
  createPaperSettle(startTime, duration, frequency, volume) {
    this.createPaperRustle(startTime, duration, frequency, volume * 0.7);
  }

  // تشغيل صوت تقليب الصفحة
  playPageFlip() {
    this.createRealisticPageFlip();
  }
}

// تصدير الكلاس
window.PageFlipSounds = PageFlipSounds;
