<!DOCTYPE html>
<html lang="ar" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title> خليها ع الله</title>

  <!-- PageFlip CSS -->
  <link rel="stylesheet" href="./dist/js/stPageFlip.css" />

  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Poppins', sans-serif;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      direction: ltr;
      overflow: hidden;
    }

    #flipbook-container {
      width: 100%;
      max-width: 1200px;
      height: 90vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 20px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    #flipbook {
      width: 100%;
      height: 100%;
      max-height: 700px;
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
      border-radius: 15px;
      overflow: hidden;
    }

    .page {
      width: 100%;
      height: 100%;
      background: white;
      border-radius: 10px;
      overflow: hidden;
    }

    .page img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .cover, .back-cover {
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #9b59b6 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 2rem;
      padding: 40px 20px;
      position: relative;
      overflow: hidden;
    }

    .cover::before, .back-cover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: shimmer 4s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .cover h1, .back-cover h1 {
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      margin-bottom: 10px;
      z-index: 1;
      position: relative;
    }

    .cover p {
      font-weight: 300;
      opacity: 0.9;
      font-size: 1.2rem;
      z-index: 1;
      position: relative;
    }

    .controls {
      display: flex;
      gap: 20px;
      align-items: center;
      justify-content: center;
    }

    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 25px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      font-family: 'Poppins', sans-serif;
      min-width: 150px;
    }

    button:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    }

    button:active {
      transform: translateY(0);
    }

    .nav-hint {
      color: white;
      font-size: 0.9rem;
      opacity: 0.8;
      text-align: center;
      font-weight: 300;
    }

    /* تحسينات للشاشات المتوسطة */
    @media (max-width: 1024px) {
      #flipbook-container {
        max-width: 95%;
        height: 85vh;
      }

      #flipbook {
        max-height: 600px;
      }
    }

    /* تحسينات للتابلت */
    @media (max-width: 768px) {
      body {
        overflow: auto;
        height: auto;
        min-height: 100vh;
        padding: 10px;
      }

      #flipbook-container {
        height: auto;
        min-height: 80vh;
        padding: 15px;
        gap: 15px;
      }

      #flipbook {
        max-height: 500px;
      }

      .controls {
        gap: 15px;
        flex-wrap: wrap;
      }

      button {
        padding: 10px 25px;
        font-size: 0.9rem;
        min-width: 130px;
      }

      .cover, .back-cover {
        font-size: 1.5rem;
        padding: 30px 15px;
      }

      .nav-hint {
        font-size: 0.8rem;
      }
    }

    /* تحسينات للموبايل */
    @media (max-width: 480px) {
      #flipbook-container {
        padding: 10px;
        gap: 10px;
        min-height: 90vh;
      }

      #flipbook {
        max-height: 400px;
      }

      .controls {
        flex-direction: column;
        gap: 10px;
        width: 100%;
      }

      button {
        width: 100%;
        max-width: 250px;
        padding: 12px 20px;
      }

      .cover, .back-cover {
        font-size: 1.3rem;
        padding: 25px 10px;
      }

      .nav-hint {
        font-size: 0.75rem;
      }
    }
  </style>
</head>
<body>

  <div id="flipbook-container">
    <div id="flipbook">
      <!-- الغلاف الأمامي -->
      <div class="page cover">
        <div>
          <h1 style="font-size: 3.5rem !important; margin-bottom: 15px; background: linear-gradient(45deg, #fff, #f0f0f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Senior 2025</h1>
          <p style="font-size: 1.4rem; font-weight: 300; letter-spacing: 2px; opacity: 0.9;">✨ Start Your Journey ✨</p>
          <div style="margin-top: 20px; font-size: 0.9rem; opacity: 0.7;">
            <p>📖 Swipe to explore</p>
          </div>
        </div>
      </div>

      <!-- صفحات صور -->

      <div class="page"><img src="./pages/page5.jpg" alt="page 5"></div>
      <div class="page"><img src="./pages/page6.jpg" alt="page 6"></div>
      <div class="page"><img src="./pages/page7.jpg" alt="page 7"></div>
      <div class="page"><img src="./pages/page8.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page9.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page10.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page11.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page12.jpg" alt="page 8"></div>

      <!-- الغلاف الخلفي -->
      <div class="page back-cover">
        <div>
          <h1 style="font-size: 3rem !important; margin-bottom: 15px; background: linear-gradient(45deg, #fff, #f0f0f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">🎓 The End 🎓</h1>
          <p style="font-size: 1.2rem; font-weight: 300; opacity: 0.9; margin-bottom: 10px;">Thank you for the memories</p>
          <p style="font-size: 1rem; opacity: 0.7;">Senior Class of 2025</p>
        </div>
      </div>
    </div>

    <!-- أزرار التنقل -->
    <div class="controls">
      <button id="prev-btn">
        ⬅️ Previous Page
      </button>
      <button id="next-btn">
        Next Page ➡️
      </button>
    </div>
    <div class="nav-hint">
      Use arrow keys or click pages to navigate
    </div>
  </div>

  <!-- PageFlip JS -->
  <script src="./dist/js/page-flip.browser.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const flipbook = document.getElementById('flipbook');
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      // إعدادات متجاوبة للكتاب
      function getFlipbookSettings() {
        const screenWidth = window.innerWidth;
        if (screenWidth <= 480) {
          return { width: 300, height: 400 };
        } else if (screenWidth <= 768) {
          return { width: 400, height: 550 };
        } else {
          return { width: 500, height: 700 };
        }
      }

      const settings = getFlipbookSettings();

      const pageFlip = new St.PageFlip(flipbook, {
        width: settings.width,
        height: settings.height,
        size: 'fixed',
        showCover: true,
        rtl: false,
        useMouseEvents: true,
        maxShadowOpacity: 0.5,
        mobileScrollSupport: false,
        disableFlipByClick: false,
      });

      pageFlip.loadFromHTML(document.querySelectorAll('.page'));

      prevBtn.addEventListener('click', () => {
        pageFlip.flipPrev();
      });

      nextBtn.addEventListener('click', () => {
        pageFlip.flipNext();
      });

      document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
          pageFlip.flipPrev();
        } else if (e.key === 'ArrowRight') {
          pageFlip.flipNext();
        }
      });

      // إعادة تحميل عند تغيير حجم الشاشة
      window.addEventListener('resize', () => {
        const newSettings = getFlipbookSettings();
        if (newSettings.width !== settings.width || newSettings.height !== settings.height) {
          location.reload();
        }
      });
    });
  </script>
</body>
</html>