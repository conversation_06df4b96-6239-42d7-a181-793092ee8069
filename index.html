<!DOCTYPE html>
<html lang="ar" dir="ltr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title> خليها ع الله</title>

  <!-- PageFlip CSS -->
  <link rel="stylesheet" href="./dist/js/stPageFlip.css" />

  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Poppins', sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      direction: ltr;
      overflow-x: hidden;
      padding: 10px;
    }

    #flipbook-container {
      width: 100%;
      max-width: 1200px;
      min-height: 90vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 20px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    #flipbook {
      width: 100%;
      height: 100%;
      max-height: 700px;
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
      border-radius: 15px;
      overflow: hidden;
      position: relative;
    }

    .page {
      width: 100%;
      height: 100%;
      background: white;
      border-radius: 10px;
      overflow: hidden;
    }

    .page img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .cover, .back-cover {
      background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #9b59b6 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 2rem;
      padding: 40px 20px;
      position: relative;
      overflow: hidden;
    }

    .cover::before, .back-cover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      animation: shimmer 4s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .cover h1, .back-cover h1 {
      font-weight: 700;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      margin-bottom: 10px;
      z-index: 1;
      position: relative;
    }

    .cover p {
      font-weight: 300;
      opacity: 0.9;
      font-size: 1.2rem;
      z-index: 1;
      position: relative;
    }

    .controls {
      display: flex;
      gap: 20px;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
    }

    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 25px;
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      font-family: 'Poppins', sans-serif;
      min-width: 150px;
    }

    button:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    }

    button:active {
      transform: translateY(0);
    }

    .nav-hint {
      color: white;
      font-size: 0.9rem;
      opacity: 0.8;
      text-align: center;
      font-weight: 300;
    }

    /* إضافة مؤشر الصوت */
    .sound-indicator {
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      border-radius: 50px;
      padding: 10px 15px;
      color: white;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 8px;
      opacity: 0;
      transform: translateY(-20px);
      transition: all 0.3s ease;
      z-index: 1000;
    }

    .sound-indicator.show {
      opacity: 1;
      transform: translateY(0);
    }

    .sound-wave {
      width: 4px;
      height: 20px;
      background: white;
      border-radius: 2px;
      animation: wave 1s infinite ease-in-out;
    }

    .sound-wave:nth-child(2) { animation-delay: 0.1s; }
    .sound-wave:nth-child(3) { animation-delay: 0.2s; }

    @keyframes wave {
      0%, 100% { transform: scaleY(0.5); }
      50% { transform: scaleY(1); }
    }

    /* تحسينات للشاشات الكبيرة جداً */
    @media (min-width: 1440px) {
      #flipbook-container {
        max-width: 1400px;
      }

      #flipbook {
        max-height: 800px;
      }

      .cover, .back-cover {
        font-size: 2.5rem;
        padding: 50px 30px;
      }
    }

    /* تحسينات للشاشات المتوسطة */
    @media (max-width: 1024px) {
      #flipbook-container {
        max-width: 95%;
        min-height: 85vh;
        padding: 18px;
      }

      #flipbook {
        max-height: 600px;
      }

      .sound-indicator {
        top: 15px;
        right: 15px;
        padding: 8px 12px;
        font-size: 0.8rem;
      }
    }

    /* تحسينات للتابلت */
    @media (max-width: 768px) {
      body {
        padding: 8px;
      }

      #flipbook-container {
        min-height: 80vh;
        padding: 15px;
        gap: 15px;
        border-radius: 15px;
      }

      #flipbook {
        max-height: 500px;
        border-radius: 12px;
      }

      .controls {
        gap: 15px;
      }

      button {
        padding: 10px 25px;
        font-size: 0.9rem;
        min-width: 130px;
      }

      .cover, .back-cover {
        font-size: 1.5rem;
        padding: 30px 15px;
      }

      .nav-hint {
        font-size: 0.8rem;
      }

      .sound-indicator {
        top: 10px;
        right: 10px;
        padding: 6px 10px;
        font-size: 0.75rem;
      }

      .sound-wave {
        width: 3px;
        height: 15px;
      }
    }

    /* تحسينات للموبايل */
    @media (max-width: 480px) {
      body {
        padding: 5px;
      }

      #flipbook-container {
        padding: 10px;
        gap: 10px;
        min-height: 95vh;
        border-radius: 12px;
      }

      #flipbook {
        max-height: 400px;
        border-radius: 10px;
      }

      .controls {
        flex-direction: column;
        gap: 10px;
        width: 100%;
      }

      button {
        width: 100%;
        max-width: 280px;
        padding: 12px 20px;
        font-size: 0.95rem;
      }

      .cover, .back-cover {
        font-size: 1.3rem;
        padding: 25px 10px;
      }

      .cover h1, .back-cover h1 {
        font-size: 2.5rem !important;
      }

      .nav-hint {
        font-size: 0.75rem;
        padding: 0 10px;
      }

      .sound-indicator {
        top: 8px;
        right: 8px;
        padding: 5px 8px;
        font-size: 0.7rem;
        border-radius: 25px;
      }

      .sound-wave {
        width: 2px;
        height: 12px;
      }
    }

    /* تحسينات للشاشات الصغيرة جداً */
    @media (max-width: 360px) {
      #flipbook-container {
        padding: 8px;
        gap: 8px;
      }

      #flipbook {
        max-height: 350px;
      }

      button {
        max-width: 250px;
        padding: 10px 15px;
        font-size: 0.9rem;
      }

      .cover, .back-cover {
        font-size: 1.1rem;
        padding: 20px 8px;
      }

      .cover h1, .back-cover h1 {
        font-size: 2rem !important;
      }
    }

    /* تحسينات للوضع الأفقي على الموبايل */
    @media (max-height: 500px) and (orientation: landscape) {
      body {
        padding: 5px;
      }

      #flipbook-container {
        min-height: 90vh;
        padding: 10px;
        gap: 10px;
      }

      #flipbook {
        max-height: 300px;
      }

      .controls {
        flex-direction: row;
        gap: 15px;
      }

      button {
        width: auto;
        min-width: 120px;
        padding: 8px 20px;
      }
    }
  </style>
</head>
<body>
  <!-- مؤشر الصوت -->
  <div class="sound-indicator" id="soundIndicator">
    <div class="sound-wave"></div>
    <div class="sound-wave"></div>
    <div class="sound-wave"></div>
    <span>🔊 Page Turn</span>
  </div>

  <div id="flipbook-container">
    <div id="flipbook">
      <!-- الغلاف الأمامي -->
      <div class="page cover">
        <div>
          <h1 style="font-size: 3.5rem !important; margin-bottom: 15px; background: linear-gradient(45deg, #fff, #f0f0f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Senior 2025</h1>
          <p style="font-size: 1.4rem; font-weight: 300; letter-spacing: 2px; opacity: 0.9;">✨ Start Your Journey ✨</p>
          <div style="margin-top: 20px; font-size: 0.9rem; opacity: 0.7;">
            <p>📖 Swipe to explore</p>
                  
  <iframe src="https://www.youtube.com/embed/JhBIbPoefn8" frameborder="0" allowfullscreen  style="border-radius: 15px; box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5); margin-top: 20px;"></iframe>

          </div>
        </div>
      </div>

      <!-- صفحات صور -->

      <div class="page"><img src="./pages/page5.jpg" alt="page 5"></div>
      <div class="page"><img src="./pages/page6.jpg" alt="page 6"></div>
      <div class="page"><img src="./pages/page7.jpg" alt="page 7"></div>
      <div class="page"><img src="./pages/page8.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page9.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page10.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page11.jpg" alt="page 8"></div>
      <div class="page"><img src="./pages/page12.jpg" alt="page 8"></div>

      <!-- الغلاف الخلفي -->
      <div class="page back-cover">
        <div>
          <h1 style="font-size: 3rem !important; margin-bottom: 15px; background: linear-gradient(45deg, #fff, #f0f0f0); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">🎓 The End 🎓</h1>
          <p style="font-size: 1.2rem; font-weight: 300; opacity: 0.9; margin-bottom: 10px;">Thank you for the memories</p>
          <p style="font-size: 1rem; opacity: 0.7;">Senior Class of 2025</p>
        </div>
      </div>
    </div>

    <!-- أزرار التنقل -->
    <div class="controls">
      <button id="prev-btn">
        ⬅️ Previous Page
      </button>
      <button id="next-btn">
        Next Page ➡️
      </button>
    </div>
    <div class="nav-hint">
      Use arrow keys or click pages to navigate
    </div>
  </div>

  <!-- PageFlip JS -->
  <script src="./dist/js/page-flip.browser.js"></script>
  <!-- Page Flip Sounds -->
  <script src="./sounds/page-flip.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const flipbook = document.getElementById('flipbook');
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');
      const soundIndicator = document.getElementById('soundIndicator');

      // إنشاء مدير الأصوات
      let soundManager = null;

      // تهيئة الأصوات
      function initSounds() {
        if (window.PageFlipSounds) {
          soundManager = new PageFlipSounds();
        }
      }

      // دالة تشغيل صوت تقليب الصفحة
      function playFlipSound() {
        try {
          if (soundManager) {
            // تشغيل صوت تقليب الصفحة الحقيقي
            soundManager.playPageFlip();
          } else {
            // إنشاء صوت بديل باستخدام Web Audio API
            createSimpleFlipSound();
          }

          // إظهار مؤشر الصوت
          showSoundIndicator();
        } catch (error) {
          // إنشاء صوت بديل
          createSimpleFlipSound();
          showSoundIndicator();
        }
      }

      // إنشاء صوت تقليب بسيط باستخدام Web Audio API
      function createSimpleFlipSound() {
        try {
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.type = 'sine';
          oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.1);

          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
          console.log('Audio not supported');
        }
      }

      // تهيئة الأصوات عند التحميل
      initSounds();

      // إظهار مؤشر الصوت
      function showSoundIndicator() {
        soundIndicator.classList.add('show');
        setTimeout(() => {
          soundIndicator.classList.remove('show');
        }, 1000);
      }

      // إعدادات متجاوبة للكتاب
      function getFlipbookSettings() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        if (screenWidth <= 360) {
          return { width: 280, height: 350 };
        } else if (screenWidth <= 480) {
          return { width: 320, height: 400 };
        } else if (screenWidth <= 768) {
          return { width: 450, height: 550 };
        } else if (screenWidth <= 1024) {
          return { width: 550, height: 650 };
        } else {
          return { width: 600, height: 750 };
        }
      }

      let settings = getFlipbookSettings();

      const pageFlip = new St.PageFlip(flipbook, {
        width: settings.width,
        height: settings.height,
        size: 'fixed',
        showCover: true,
        rtl: false,
        useMouseEvents: true,
        maxShadowOpacity: 0.5,
        mobileScrollSupport: false,
        disableFlipByClick: false,
      });

      pageFlip.loadFromHTML(document.querySelectorAll('.page'));

      // إضافة أحداث الصوت
      pageFlip.on('flip', (e) => {
        playFlipSound();
      });



      prevBtn.addEventListener('click', () => {
        pageFlip.flipPrev();
      });

      nextBtn.addEventListener('click', () => {
        pageFlip.flipNext();
      });

      document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
          pageFlip.flipPrev();
        } else if (e.key === 'ArrowRight') {
          pageFlip.flipNext();
        }
      });

      // إعادة تحميل عند تغيير حجم الشاشة
      window.addEventListener('resize', () => {
        const newSettings = getFlipbookSettings();
        if (newSettings.width !== settings.width || newSettings.height !== settings.height) {
          location.reload();
        }
      });

      document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
          pageFlip.flipPrev();
        } else if (e.key === 'ArrowRight') {
          pageFlip.flipNext();
        }
      });
    });
  </script>
</body>
</html>