/* تحسينات إضافية للكتاب المتجاوب */

/* تأثيرات الحركة المحسنة */
.page {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* تحسين الأزرار */
.controls button {
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.controls button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.controls button:hover::after {
  left: 100%;
}

/* تحسين مؤشر الصوت */
.sound-indicator {
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.sound-wave {
  background: linear-gradient(45deg, #fff, #f0f0f0);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسينات للغلاف */
.cover, .back-cover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
}

.cover::after, .back-cover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* تحسين الصور */
.page img {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.page img:hover {
  filter: brightness(1.1) contrast(1.05);
}

/* تأثيرات التحميل */
.loading {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .page img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  #flipbook-container {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .sound-indicator {
    background: rgba(0, 0, 0, 0.3);
    color: #ecf0f1;
  }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .page:hover {
    transform: none;
  }
  
  .sound-wave {
    animation: none;
  }
}

/* تحسينات للطباعة */
@media print {
  body {
    background: white;
  }
  
  #flipbook-container {
    background: white;
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .controls, .sound-indicator {
    display: none;
  }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1920px) {
  #flipbook-container {
    max-width: 1600px;
  }
  
  #flipbook {
    max-height: 900px;
  }
  
  .cover, .back-cover {
    font-size: 3rem;
    padding: 60px 40px;
  }
}

/* تحسينات للشاشات فائقة العرض */
@media (min-aspect-ratio: 21/9) {
  #flipbook-container {
    flex-direction: row;
    gap: 40px;
  }
  
  .controls {
    flex-direction: column;
    gap: 20px;
  }
}

/* تحسينات للشاشات الطويلة */
@media (max-aspect-ratio: 9/16) {
  #flipbook {
    max-height: 60vh;
  }
  
  .controls {
    margin-top: 20px;
  }
}
